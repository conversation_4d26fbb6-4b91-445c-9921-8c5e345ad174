/**
 * SyncCheckpoint Model
 * Tracks sync progress and enables resume capability
 */

import mongoose from 'mongoose';

const syncCheckpointSchema = new mongoose.Schema({
  // Type of sync (commissions, agents)
  syncType: {
    type: String,
    required: true,
    enum: ['commissions', 'agents']
  },

  // Last successful sync timestamp
  lastSyncTime: {
    type: Date,
    required: true
  },

  // Status of current sync
  status: {
    type: String,
    enum: ['idle', 'in_progress', 'completed', 'failed'],
    default: 'idle'
  },

  // Progress tracking
  totalRecords: {
    type: Number,
    default: 0
  },

  processedRecords: {
    type: Number,
    default: 0
  },

  successfulRecords: {
    type: Number,
    default: 0
  },

  failedRecords: {
    type: Number,
    default: 0
  },

  // Error tracking
  lastError: String,
  errorCount: {
    type: Number,
    default: 0
  },

  // Sync session info
  syncStartTime: Date,
  syncEndTime: Date,

  // Additional metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'synccheckpoints'
});

// No indexes - removed to avoid conflicts

const SyncCheckpoint = mongoose.model('SyncCheckpoint', syncCheckpointSchema);

export default SyncCheckpoint;
