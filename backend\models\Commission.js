/**
 * Commission Model for Celigo Data
 * Stores commission data from Celigo API
 */

import mongoose from 'mongoose';

const commissionSchema = new mongoose.Schema({
  // Raw data from Celigo for reference - store complete response
  rawData: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },

  // Extracted fields for quick access
  celigoId: {
    type: String
  },

  agentNPN: {
    type: String
  },

  amount: {
    type: String
  },

  carrier: {
    type: String
  },

  status: {
    type: String
  },

  date: {
    type: String
  },

  policyNumber: {
    type: String
  },

  firstName: {
    type: String
  },

  lastName: {
    type: String
  },

  // Sync tracking
  syncedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'commissions'
});

// No indexes - removed to avoid conflicts

const Commission = mongoose.model('Commission', commissionSchema);

export default Commission;
