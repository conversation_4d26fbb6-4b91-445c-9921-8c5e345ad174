/**
 * Agent Model for Celigo Contract Data
 * Stores agent/contract data from Celigo API
 */

import mongoose from 'mongoose';

const agentSchema = new mongoose.Schema({
  // Raw data from Celigo for reference - store complete response
  rawData: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },

  // Extracted fields for quick access
  celigoId: {
    type: String
  },

  contractId: {
    type: String
  },

  agentNPN: {
    type: String
  },

  firstName: {
    type: String
  },

  lastName: {
    type: String
  },

  contractStatus: {
    type: String
  },

  insuranceCompany: {
    type: String
  },

  companyName: {
    type: String
  },

  // Sync tracking
  syncedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'agents'
});

// Create text search indexes for search functionality
agentSchema.index({
  agentNPN: 'text',
  firstName: 'text',
  lastName: 'text',
  companyName: 'text',
  contractStatus: 'text',
  insuranceCompany: 'text'
});

const Agent = mongoose.model('Agent', agentSchema);

export default Agent;
